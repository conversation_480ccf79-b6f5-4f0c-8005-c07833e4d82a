{"language": {"switchToEnglish": "切换到英文", "switchToChinese": "切换到中文", "en": "EN", "zh": "中"}, "theme": {"switchToLight": "切换到亮色模式", "switchToDark": "切换到暗色模式"}, "settings": {"promptConfig": "提示词配置", "promptsDescription": "配置项目中使用的各类自定义提示词，可用于人工干预数据集的生成效果。", "globalPrompt": "全局提示词", "questionPrompt": "生成问题提示词", "answerPrompt": "生成答案提示词", "labelPrompt": "问题打标提示词", "domainTreePrompt": "构建领域树提示词", "globalPromptPlaceholder": "请输入全局提示词（慎用，可能影响整体生成效果）", "questionPromptPlaceholder": "请输入自定义生成问题的提示词", "answerPromptPlaceholder": "请输入自定义生成答案的提示词", "labelPromptPlaceholder": "请输入自定义问题打标的提示词（暂不支持配置）", "domainTreePromptPlaceholder": "请输入自定义构建领域树的提示词", "loadPromptsFailed": "加载提示词配置失败", "savePromptsSuccess": "保存提示词配置成功", "savePromptsFailed": "保存提示词配置失败", "title": "项目设置", "basicInfo": "基本信息", "modelConfig": "模型配置", "taskConfig": "任务配置", "tabsAriaLabel": "设置选项卡", "idNotEditable": "项目 ID 不可编辑", "saveBasicInfo": "保存基本信息", "saveSuccess": "保存成功", "saveFailed": "保存失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "fetchTasksFailed": "获取任务配置失败", "saveTasksFailed": "保存任务配置失败", "textSplitSettings": "文本分块设置", "minLength": "最小长度", "maxLength": "最大分割长度", "textSplitDescription": "调整文本分割的长度范围，影响分割结果的粒度", "splitType": "分块策略", "splitTypeMarkdown": "文档结构分块（Markdown）", "splitTypeMarkdownDesc": "根据文档中的标题自动分割文本，保持语义完整性，适合结构化清晰的 Markdown 文档", "splitTypeRecursive": "文本结构分块（自定义分隔符）", "splitTypeRecursiveDesc": "递归地尝试多级分隔符（可配置），先用优先级高的分隔符，再用次级分隔符，适合复杂文档", "splitTypeText": "固定长度分块（字符）", "splitTypeTextDesc": "按指定分隔符（可配置）切分文本，然后按指定长度组合，适合普通文本文件", "splitTypeToken": "固定长度分块（Token）", "splitTypeTokenDesc": "基于 Token 数量（而非字符数）分块", "splitTypeCode": "程序代码智能分块", "splitTypeCodeDesc": "根据不同编程语言的语法结构进行智能分块，避免在语法不完整处分割", "codeLanguage": "代码语言", "codeLanguageHelper": "选择要分块的代码语言，会根据语言特性进行智能分块", "chunkSize": "块大小", "chunkOverlap": "块重叠长度", "separator": "分隔符", "separatorHelper": "用于分割文本的分隔符，如 \n\n 表示空行", "separators": "分隔符列表", "separatorsInput": "分隔符（逗号分隔）", "separatorsHelper": "用逗号分隔的分隔符列表，按优先级排序", "questionGenSettings": "问题生成设置", "questionGenLength": "{{length}} 个字符生成一个问题", "questionMaskRemovingProbability": "将 {{probability}}% 问题结尾的问号去除", "questionGenDescription": "设置生成问题的最大长度", "huggingfaceSettings": "Hugging Face 设置", "datasetUpload": "数据集上传设置", "huggingfaceToken": "Hugging Face <PERSON>", "huggingfaceNotImplemented": "Hugging Face 功能暂未实现", "concurrencyLimit": "并发限制数量", "concurrencyLimitHelper": "限制同时生成问题、生成数据集的任务数量", "saveTaskConfig": "保存任务配置", "pdfSettings": "PDF文件转换配置", "minerUToken": "PDF 转换（MinerU API）Token 配置", "minerUHelper": "MinerU Token 只有14天有效期，请及时更换Token", "vision": "自定义视觉模型选择", "visionConcurrencyLimit": "PDF 转换（自定义视觉模型）并发数量"}, "questions": {"autoGenerateDataset": "自动生成数据集", "autoGenerateDatasetTip": "创建后台批量处理任务：自动查询待生成答案的问题并生成答案", "filterAll": "全部问题", "filterAnswered": "已生成答案", "filterUnanswered": "未生成答案", "title": "问题管理", "confirmDeleteTitle": "确认删除问题", "confirmDeleteContent": "您确定要删除问题\"{{question}}\"吗？此操作不可恢复。", "deleting": "正在删除问题...", "batchDeleteTitle": "确认批量删除问题", "batchDeleting": "正在删除 {{count}} 个问题...", "deleteSuccess": "问题删除成功", "deleteFailed": "删除问题失败", "batchDeleteSuccess": "成功删除 {{count}} 个问题", "batchDeletePartial": "删除完成，成功: {{success}}, 失败: {{failed}}", "batchDeleteFailed": "批量删除问题失败", "noQuestionsSelected": "请先选择问题", "batchGenerateStart": "开始生成 {{count}} 个问题的数据集", "invalidQuestionKey": "无效的问题键", "listView": "列表视图", "treeView": "领域树视图", "selectAll": "全选", "selectedCount": "已选择 {{count}} 个问题", "totalCount": "共 {{count}} 个问题", "searchPlaceholder": "搜索问题或标签...", "deleteSelected": "删除所选", "batchGenerate": "批量构造数据集", "generating": "正在生成数据集", "generatingProgress": "已完成: {{completed}}/{{total}}", "generatedCount": "已成功生成: {{count}} 个", "pleaseWait": "请耐心等待，正在处理中...", "createSuccess": "问题创建成功", "updateSuccess": "问题更新成功", "operationSuccess": "操作成功", "operationFailed": "操作失败", "editQuestion": "编辑问题", "questionContent": "问题内容", "selectChunk": "选择文本块", "selectTag": "选择标签", "createQuestion": "创建问题", "questionPlaceholder": "请输入问题内容", "noChunkSelected": "请先选择文本块", "noTagSelected": "请选择标签", "deleteConfirm": "确认要删除这个问题吗？"}, "common": {"jumpTo": "跳转至", "unknownError": "未知错误", "create": "创建", "confirm": "确认", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "complete": "完成", "close": "关闭", "add": "添加", "remove": "删除", "loading": "加载中...", "yes": "是", "no": "否", "confirmDelete": "确认删除吗？此操作不可撤销！", "saving": "保存中...", "deleting": "删除中...", "actions": "操作", "confirmDeleteDataSet": "确认删除数据集吗？操作不可撤销！", "noData": "无", "failed": "失败", "success": "成功", "backToList": "返回列表", "label": "标签", "confirmDeleteDescription": "确认删除吗？此操作不可恢复。", "more": "更多", "fetchError": "获取数据出错", "confirmDeleteQuestion": "确认删除此问题吗？此操作不可恢复。", "deleteSuccess": "删除成功", "syncOldData": "同步文件数据", "copy": "复制", "copied": "已复制", "enabled": "已启用", "disabled": "已禁用", "generating": "正在生成...", "language": "语言"}, "home": {"title": "Sleep Magician", "subtitle": "专业的睡眠数据分析与管理平台，助力睡眠健康研究", "createProject": "创建睡眠项目", "searchDataset": "搜索睡眠数据集"}, "projects": {"reuseConfig": "复用模型配置", "noReuse": "不复用配置", "selectProject": "选择项目", "fetchFailed": "获取项目列表失败", "fetchError": "获取项目列表出错", "loading": "正在加载您的项目...", "createFailed": "创建项目失败", "createError": "创建项目出错", "createNew": "创建新项目", "saveFailed": "保存项目失败", "id": "项目ID", "name": "项目名称", "description": "项目描述", "questions": "问题", "datasets": "数据集", "lastUpdated": "最后更新", "viewDetails": "查看详情", "createFirst": "创建第一个项目", "noProjects": "暂无项目", "notExist": "项目不存在", "createProject": "创建项目", "deleteConfirm": "确认删除项目吗？此操作不可恢复。", "deleteSuccess": "项目删除成功", "deleteFailed": "删除项目失败", "backToHome": "返回首页", "deleteConfirmTitle": "确认删除项目", "title": "项目管理", "openDirectory": "打开项目目录"}, "textSplit": {"autoGenerateQuestions": "自动提取问题", "autoGenerateQuestionsTip": "创建后台批量处理任务：自动查询待生成问题的文本块并提取问题", "exportChunks": "导出文本块", "allChunks": "全部文本块", "generatedQuestions2": "已生成问题", "ungeneratedQuestions": "未生成问题", "noFilesUploaded": "暂未上传文件", "unknownFile": "未知文件", "fetchFilesFailed": "获取文件列表出错", "editTag": "编辑标签", "deleteTag": "删除标签", "addTag": "添加标签", "selectedCount": "已选择 {{count}} 个文本块", "totalCount": "共 {{count}} 个文本块", "batchGenerateQuestions": "批量生成问题", "uploadedDocuments": "已上传 {{count}}个文档", "title": "文献处理", "uploadNewDocument": "上传新文献", "selectFile": "选择文件（支持多个）", "markdownOnly": "目前仅支持上传 Markdown (.md) 格式文件（建议上传同一领域的文献）", "supportedFormats": "支持的格式: .pdf .md, .txt, .docx（建议上传同一领域的文献）", "uploadAndProcess": "上传并处理文件", "selectedFiles": "已选择文件（{{count}}）", "oneFileMessage": "一个项目限制处理一个文件，如需上传新文件请先删除现有文件", "mutilFileMessage": "上传新文件后会重新构建领域树", "noChunks": "暂无文本块，请先上传并处理文献", "chunkDetails": "文本块详情: {{chunkId}}", "fetchChunksFailed": "获取文本块失败", "fetchChunksError": "获取文本块出错", "fileResultReceived": "获取到文件结果", "fileUploadSuccess": "文件上传成功", "splitTextFailed": "文本分割失败", "splitTextError": "文本分割出错", "deleteChunkFailed": "删除文本块失败", "deleteChunkError": "删除文本块出错", "selectModelFirst": "请先选择一个模型，可以在顶部导航栏选择", "modelNotAvailable": "选择的模型不可用，请重新选择", "generateQuestionsFailed": "为文本块 {{chunkId}} 生成问题失败", "questionsGenerated": "已生成 {{total}} 个问题", "customSplitMode": "自定义分块模式", "customSplitInstructions": "选择文本内容以添加分块点。系统会在选中位置添加分割标记。", "splitPointsList": "已添加的分块点", "saveSplitPoints": "保存分块点", "confirmCustomSplitTitle": "确认替换原有分块", "confirmCustomSplitMessage": "注意：自定义分块将替换该文献之前自动分块的结果。确定要继续保存吗？", "customSplitSuccess": "自定义分块保存成功", "customSplitFailed": "自定义分块保存失败", "missingRequiredData": "缺少必要的数据", "chunksPreview": "分块字数预览", "chunk": "文本块", "characters": "字", "questionsGeneratedSuccess": "成功为文本块生成了 {{total}} 个问题", "generateQuestionsForChunkFailed": "为文本块 {{chunkId}} 生成问题失败", "generateQuestionsForChunkError": "为文本块 {{chunkId}} 生成问题出错", "generateQuestionsError": "生成问题出错", "partialSuccess": "部分文本块生成问题成功 ({{successCount}}/{{total}})，{{errorCount}} 个文本块失败", "allSuccess": "成功为 {{successCount}} 个文本块生成了 {{totalQuestions}} 个问题", "fileDeleted": "文件 {{fileName}} 已删除，刷新文本块列表", "tabs": {"smartSplit": "智能分割", "domainAnalysis": "领域分析"}, "loading": "加载中...", "fetchingDocuments": "正在获取文献数据", "processing": "处理中...", "progressStatus": "已选择 {{total}} 个文本块，已处理完成 {{completed}} 个", "processingPleaseWait": "正在努力处理中，请稍候！", "oneFileLimit": "已有上传文件，不允许选择新文件", "unsupportedFormat": "不支持的文件格式: {{files}}", "modelInfoParseError": "解析模型信息失败", "uploadFailed": "上传失败，请刷新页面后重试！", "uploadSuccess": "成功上传 {{count}} 个文件", "deleteFailed": "删除文件失败", "deleteSuccess": "文件 {{fileName}} 已成功删除", "generatedQuestions": "{{count}} 个问题", "viewDetails": "查看详情", "generateQuestions": "生成问题", "charsCount": "字符", "pdfProcess": "[beta]检测到PDF文件，请选择 PDF 文件处理方式！", "pdfProcessStatus": "共 {{total}} 个文件，{{completed}} 个已处理完成", "pdfPageProcessStatus": "正在处理 {{fileName}} 共{{total}}页，{{completed}}页已完成转换", "pdfProcessing": "正在转换文件...", "pdfProcessingFailed": "文件处理失败！", "selectPdfProcessingStrategy": "请选择PDF文件处理方式：", "pdfProcessingStrategyDefault": "默认", "pdfProcessingStrategyDefaultHelper": "使用内置PDF解析策略", "pdfProcessingStrategyMinerUHelper": "使用MinerU API解析，请先配置MinerU API Token", "pdfProcessingStrategyVision": "自定义视觉模型", "pdfProcessingStrategyVisionHelper": "使用自定义视觉模型解析", "pdfProcessingToast": "上传文件成功，系统将创建后台任务解析文件！", "pdfProcessingLoading": "正在执行文件处理任务，请等待任务完成后再上传新文件...", "pdfProcessingWaring": "正在执行文件处理任务，建议当任务完成后再进行其他操作，否则可能会影响数据生成质量！", "basicPdfParsing": "基础 PDF 解析", "basicPdfParsingDesc": "可识别简单的 PDF 文件，包括关键目录结构，速度更快", "mineruApiDesc": "可识别复杂 PDF 文件，包括公式、图表（需要配置 MinerU API Key）", "mineruApiDescDisabled": "请先到【项目配置 - 任务配置】设置 MinerU Token", "mineruWebPlatform": "MinerU 在线平台解析", "mineruWebPlatformDesc": "可识别复杂 PDF 文件，包括公式、图表（需跳转到其他网站）", "mineruSelected": "已选择使用 MinerU 解析PDF", "customVisionModel": "自定义视觉模型解析", "customVisionModelDesc": "可识别复杂 PDF 文件，包括公式、图表（需在模型配置增加视觉模型配置）", "customVisionModelSelected": "已选择使用视觉大模型 {{name}}（{{provider}}） 解析PDF）", "defaultSelected": "已选择使用默认内置策略解析PDF", "download": "下载文献", "deleteFile": "删除文献", "viewChunk": "查看文本块", "editChunk": "编辑文本块 {{chunkId}}", "editChunkSuccess": "文本块编辑成功", "editChunkFailed": "文本块编辑失败", "editChunkError": "编辑文本块时出错", "deleteFileWarning": "警告：删除文献将同时删除以下相关内容", "deleteFileWarningChunks": "所有关联的文本块", "deleteFileWarningQuestions": "所有文本块生成的问题", "deleteFileWarningDatasets": "所有问题生成的数据集", "domainTree": {"firstUploadTitle": "领域树生成", "uploadTitle": "文献上传 - 领域树处理", "deleteTitle": "文献删除 - 领域树处理", "reviseOption": "修订领域树", "reviseDesc": "针对新增或删除的文献信息，对当前的领域树进行修正，只影响变更的部分", "rebuildOption": "重建领域树", "rebuildDesc": "基于所有文献的目录信息重新生成完整的领域树", "keepOption": "保持不变", "keepDesc": "保持当前领域树结构不变，不做任何修改"}}, "domain": {"title": "领域知识树", "addRootTag": "添加一级标签", "addFirstTag": "添加第一个标签", "noTags": "暂无领域标签树数据", "docStructure": "文档目录结构", "noToc": "暂无目录结构，请先上传并处理文献", "editTag": "编辑标签", "deleteTag": "删除标签", "addChildTag": "添加子标签", "deleteTagConfirmTitle": "删除标签", "deleteTagConfirmMessage": "您确定要删除标签 \"{{tag}}\" 吗？", "deleteWarning": "此操作将删除该标签及其所有子标签、问题和数据集，且无法恢复！", "dialog": {"addTitle": "添加标签", "editTitle": "编辑标签", "addChildTitle": "添加子标签", "inputRoot": "请输入新的一级标签名称", "inputEdit": "请编辑标签名称", "inputChild": "请为\"{label}\"添加子标签", "labelName": "标签名称", "saving": "保存中...", "save": "保存", "deleteConfirm": "确定要删除标签\"{label}\"吗？", "deleteWarning": "此操作将同时删除所有子标签，且无法恢复。", "emptyLabel": "标签名称不能为空"}, "tabs": {"tree": "领域树", "structure": "目录结构"}, "errors": {"saveFailed": "保存标签失败"}, "messages": {"updateSuccess": "标签更新成功"}}, "export": {"alpacaSettings": "Alpaca 格式设置", "questionFieldType": "问题字段类型", "useInstruction": "使用 instruction 字段", "useInput": "使用 input 字段", "customInstruction": "自定义 instruction 字段内容", "instructionPlaceholder": "请输入固定的指令内容", "instructionHelperText": "当使用 input 字段时，可以在这里指定固定的 instruction 内容", "title": "导出数据集", "format": "数据集风格", "fileFormat": "文件格式", "systemPrompt": "系统提示词", "systemPromptPlaceholder": "请输入系统提示词...", "onlyConfirmed": "仅导出已确认数据", "example": "格式示例", "confirmExport": "确认导出", "includeCOT": "包含思维链", "cotDescription": "包含最终答案前的推理过程", "customFormat": "自定义格式", "customFormatSettings": "自定义格式设置", "questionFieldName": "问题字段名", "answerFieldName": "答案字段名", "cotFieldName": "思维链字段名", "includeLabels": "包含标签", "includeChunk": "包含文本块", "localTab": "导出到本地", "llamaFactoryTab": "在 LLaMA Factory 中使用", "huggingFaceTab": "上传至 Hugging Face", "configExists": "已存在配置文件", "configPath": "配置文件路径", "updateConfig": "更新 LLaMA Factory 配置", "noConfig": "暂无配置文件，点击下方按钮生成", "generateConfig": "生成 LLaMA Factory 配置", "huggingFaceComingSoon": "HuggingFace 导出功能即将推出", "uploadToHuggingFace": "上传至 HuggingFace", "datasetName": "数据集名称", "datasetNameHelp": "格式：用户名/数据集名称", "privateDataset": "私有数据集", "datasetSettings": "数据集设置", "exportOptions": "导出选项", "uploadSuccess": "数据集已成功上传至 HuggingFace", "viewOnHuggingFace": "在 HuggingFace查看", "noTokenWarning": "未找到 HuggingFace 令牌。请在项目设置中配置令牌。", "goToSettings": "前往设置", "tokenHelp": "您可以从HuggingFace设置页面获取令牌"}, "datasets": {"loadingDataset": "正在加载数据集详情...", "datasetNotFound": "未找到数据集", "optimizeTitle": "AI 优化", "optimizeAdvice": "优化建议", "optimizePlaceholder": "请输入您对答案的改进建议，AI将根据您的建议优化答案和思维链", "generatingDataset": "正在生成数据集", "aiOptimizeAdvicePlaceholder": "请输入您对答案的改进建议，AI将根据您的建议优化答案和思维链", "aiOptimizeAdvice": "请输入您对答案的改进建议，AI将根据您的建议优化答案和思维链", "aiOptimize": "AI 智能优化", "generating": "正在生成数据集", "partialSuccess": "部分问题生成数据集成功 ({{successCount}}/{{total}})，{{failCount}} 个问题失败", "generateError": "生成数据集失败", "management": "数据集管理", "question": "问题", "filterAll": "全部", "filterConfirmed": "已确认", "filterUnconfirmed": "未确认", "createdAt": "创建时间", "model": "使用模型", "domainTag": "领域标签", "cot": "思维链", "answer": "回答", "chunkId": "文本块", "confirmed": "已确认", "noTag": "无标签", "noData": "暂无数据", "rowsPerPage": "每页行数", "pagination": "{{from}}-{{to}} 共 {{count}}", "confirmDeleteMessage": "确定要删除这个数据集吗？这个操作不可撤销。", "questionLabel": "问题", "fetchFailed": "获取数据集失败", "deleteFailed": "删除数据集失败", "deleteSuccess": "删除成功", "exportSuccess": "数据集导出成功", "exportFailed": "导出失败", "loading": "加载数据集...", "stats": "共 {{total}} 个数据集，已确认 {{confirmed}} 个（{{percentage}}%）", "selected": "共选中{{ count }}个数据集", "batchconfirmDeleteMessage": "您确定要删除选中的 {{count}} 个数据集吗？此操作不可恢复。", "batchDelete": "批量删除", "batchDeleteProgress": "已完成: {{completed}}/{{total}}", "batchDeleteCount": "已生成: {{count}}", "searchPlaceholder": "搜索数据集...", "viewDetails": "查看详情", "datasetDetail": "数据集详情", "metadata": "元数据", "confirmSave": "确认保留", "uncategorized": "未分类", "questionCount": "{{count}} 个问题", "source": "来源", "generateDataset": "生成数据集", "generateNotImplemented": "生成数据集功能未实现", "generateSuccess": "成功生成数据集：{{question}}", "generateFailed": "生成数据集失败：{{error}}", "noTagsAndQuestions": "暂无标签和问题", "answerCount": "{{count}} 个答案", "answered": "已生成答案", "enableShortcuts": "翻页快捷键", "shortcutsHelp": "按 ← 向前，按 → 向后，按 y 确认，按 d 删除"}, "update": {"newVersion": "新版本", "newVersionAvailable": "发现新版本", "currentVersion": "当前版本", "latestVersion": "最新版本", "downloadNow": "立即下载", "downloading": "正在下载:", "installNow": "立即安装", "updating": "更新中...", "updateNow": "立即更新", "viewRelease": "下载最新版本", "checking": "正在检查更新...", "noUpdates": "已是最新版本", "updateError": "更新出错", "updateSuccess": "更新成功", "restartRequired": "需要重启应用", "restartNow": "立即重启", "restartLater": "稍后重启"}, "datasetSquare": {"title": "数据集广场", "subtitle": "发现和探索各种公开数据集资源，助力您的模型训练和研究", "searchPlaceholder": "搜索数据集关键词...", "searchVia": "通过", "categoryTitle": "数据集分类", "categories": {"all": "全部", "popular": "热门推荐", "chinese": "中文资源", "english": "英文资源", "research": "研究数据", "multimodal": "多模态"}, "foundResources": "找到 {{count}} 个数据集资源", "currentFilter": "当前筛选: {{category}}", "noDatasets": "没有找到符合条件的数据集", "tryOtherCategories": "请尝试其他分类或返回全部数据集查看", "dataset": "数据集", "viewDataset": "查看数据集"}, "playground": {"title": "模型测试", "selectModelFirst": "请选择至少一个模型", "sendFirstMessage": "发送第一条消息开始测试", "inputMessage": "输入消息...", "send": "发送", "outputMode": "输出方式", "normalOutput": "普通输出", "streamingOutput": "流式输出", "clearConversation": "清空对话", "selectModelMax3": "选择模型（最多3个）", "reasoningProcess": "推理过程"}, "chunks": {"title": "文本块", "defaultTitle": "默认标题"}, "documentation": "文档", "models": {"configNotFound": "未找到模型配置", "parseError": "解析模型配置失败", "fetchFailed": "获取模型失败", "saveFailed": "保存模型配置失败", "pleaseSelectModel": "请至少选择一个模型", "title": "模型管理", "add": "添加模型", "unselectedModel": "未选择模型", "unconfiguredAPIKey": "未配置 API Key", "saveAllModels": "保存所有模型配置", "edit": "编辑", "delete": "删除", "modelName": "模型名称（可自定义输入）", "endpoint": "接口地址", "apiKey": "API密钥", "provider": "提供商（可自定义输入）", "localModel": "本地模型", "apiKeyConfigured": "API Key 已经配置", "apiKeyNotConfigured": "API Key 未配置", "temperature": "模型温度", "maxTokens": "最大生成 Token 数", "type": "模型标签", "text": "语言模型", "vision": "视觉模型", "typeTips": "如果希望使用自定义视觉模型解析PDF请务必配置至少一个视觉大模型", "refresh": "刷新模型列表"}, "stats": {"ongoingProjects": "正在运行的项目", "questionCount": "问题数量", "generatedDatasets": "已生成的数据集", "supportedModels": "已支持的模型"}, "migration": {"title": "【重要】历史数据迁移", "description": "为了提升大量数据集的检索性能，自 1.3.1 版本起，MED-Dataset 将文件存储方式变更为本地数据库存储，检测到您有历史项目尚未进行迁移，在完成迁移前，您将无法访问这些项目，请尽快完成迁移！", "projectsList": "未迁移的项目", "migrate": "开始迁移", "migrating": "迁移中...", "success": "成功迁移 {{count}} 个项目", "failed": "迁移失败", "checkFailed": "检查未迁移项目失败", "checkError": "检查未迁移项目出错", "starting": "正在启动迁移任务...", "processing": "正在处理迁移任务...", "completed": "迁移已完成", "startFailed": "启动迁移任务失败", "statusFailed": "获取迁移状态失败", "taskNotFound": "迁移任务不存在", "progressStatus": "已迁移 {{completed}}/{{total}} 个项目", "openDirectory": "打开项目目录", "deleteDirectory": "删除项目目录", "confirmDelete": "确定要删除此项目目录吗？此操作不可恢复。", "openDirectoryFailed": "打开项目目录失败", "deleteDirectoryFailed": "删除项目目录失败"}, "distill": {"title": "数据蒸馏", "generateRootTags": "生成顶级标签", "generateSubTags": "生成子标签", "generateQuestions": "生成问题", "generateRootTagsTitle": "生成顶级领域标签", "generateSubTagsTitle": "为 {{parentTag}} 生成子标签", "generateQuestionsTitle": "为 {{tag}} 生成问题", "parentTag": "父标签", "parentTagPlaceholder": "请输入父标签名称（如：体育、科技等）", "parentTagHelp": "输入一个领域主题，系统将基于此生成相关标签", "tagCount": "标签数量", "tagCountHelp": "输入要生成的标签数量，最大为100个", "questionCount": "问题数量", "questionCountHelp": "输入要生成的问题数量，最大为100个", "generatedTags": "已生成的标签", "generatedQuestions": "已生成的问题", "generateTags": "生成标签", "tagPath": "标签路径", "noTags": "暂无标签", "noQuestions": "暂无问题", "clickGenerateButton": "点击上方的生成按钮开始创建标签", "selectModelFirst": "请先选择一个模型", "selectModel": "选择模型", "generateTagsError": "生成标签失败", "generateQuestionsError": "生成问题失败", "deleteTagConfirmTitle": "确认要删除标签吗？将关联删除所有该标签下的子标签、问题、数据集", "unknownTag": "未知标签", "autoDistillButton": "全自动蒸馏数据集", "autoDistillTitle": "全自动蒸馏数据集配置", "distillTopic": "蒸馏主题", "tagLevels": "标签层级", "tagLevelsHelper": "设置层级数量，最大为{{max}}级", "tagsPerLevel": "每层标签数量", "tagsPerLevelHelper": "每个父标签下生成的子标签数量，最大为{{max}}个", "questionsPerTag": "每个标签问题数量", "questionsPerTagHelper": "每个叶子标签生成的问题数量，最大为{{max}}个", "estimationInfo": "任务预估信息", "estimatedTags": "预计生成标签数量", "estimatedQuestions": "预计生成问题数量", "currentTags": "当前标签数量", "currentQuestions": "当前问题数量", "newTags": "预计新增标签数量", "newQuestions": "预计新增问题数量", "startAutoDistill": "开始自动蒸馏", "autoDistillProgress": "自动蒸馏进度", "overallProgress": "整体进度", "tagsProgress": "标签构建进度", "questionsProgress": "问题生成进度", "currentStage": "当前阶段", "realTimeLogs": "实时日志", "waitingForLogs": "等待日志输出...", "autoDistillStarted": "{{time}} 自动蒸馏任务开始", "autoDistillInsufficientError": "当前配置不会产生新的标签或问题，请调整参数", "stageInitializing": "初始化中...", "stageBuildingLevel1": "正在构建第一层标签", "stageBuildingLevel2": "正在构建第二层标签", "stageBuildingLevel3": "正在构建第三层标签", "stageBuildingLevel4": "正在构建第四层标签", "stageBuildingLevel5": "正在构建第五层标签", "stageBuildingQuestions": "正在生成问题", "stageBuildingDatasets": "正在生成数据集", "stageCompleted": "任务已完成", "datasetsProgress": "数据集生成进度", "rootTopicHelperText": "默认以项目名称作为顶级蒸馏主题，如需更改，请到项目设置中更改项目名称。", "addChildTag": "生成子标签"}, "tasks": {"pending": "有 {{count}} 个任务处理中", "completed": "全部任务已完成", "title": "任务管理中心", "loading": "加载任务列表...", "empty": "暂无任务记录", "confirmDelete": "确认删除该任务？", "confirmAbort": "确认中断该任务？任务将停止执行。", "deleteSuccess": "任务已删除", "deleteFailed": "删除任务失败", "abortSuccess": "任务已中断", "abortFailed": "中断任务失败", "status": {"processing": "处理中", "completed": "已完成", "failed": "失败", "aborted": "已中断", "unknown": "未知"}, "types": {"text-processing": "文献处理", "question-generation": "问题生成", "answer-generation": "答案生成", "data-distillation": "数据蒸馈", "pdf-processing": "PDF解析"}, "filters": {"status": "任务状态", "type": "任务类型"}, "actions": {"refresh": "刷新任务列表", "delete": "删除任务", "abort": "中断任务"}, "table": {"type": "任务类型", "status": "状态", "progress": "进度", "success": "成功数量", "failed": "失败数量", "createTime": "创建时间", "endTime": "完成时间", "duration": "运行时间", "model": "使用模型", "detail": "任务详情", "actions": "操作"}, "duration": {"seconds": "{{seconds}}秒", "minutes": "{{minutes}}分 {{seconds}}秒", "hours": "{{hours}}小时 {{minutes}}分"}, "fetchFailed": "获取任务列表失败"}, "gaPairs": {"title": "文体-受众对管理", "loading": "正在加载文体-受众对...", "addPair": "添加文体-受众对", "saveChanges": "保存更改", "saving": "保存中...", "restoreBackup": "恢复备份", "noGaPairsTitle": "未找到文体-受众对", "noGaPairsDescription": "为此文件生成AI驱动的文体-受众对", "generateGaPairs": "生成文体-受众对", "generating": "生成中...", "generateMore": "生成更多文体-受众对", "activePairs": "活跃的文体-受众对 ({{active}}/{{total}})", "pairNumber": "文体-受众对 #{{number}}", "active": "活跃", "deleteTooltip": "删除文体-受众对", "genre": "文体", "genreDescription": "文体描述", "audience": "受众", "audienceDescription": "受众描述", "addDialogTitle": "添加新的文体-受众对", "genreTitle": "文体标题", "audienceTitle": "受众标题", "genreTitlePlaceholder": "请输入文体标题...", "genreDescPlaceholder": "详细描述该文体...", "audienceTitlePlaceholder": "请输入受众标题...", "audienceDescPlaceholder": "详细描述目标受众...", "cancel": "取消", "addPairButton": "添加文体-受众对", "requiredFields": "文体标题和受众标题为必填项", "restoredFromBackup": "已从备份恢复", "allPairsDeleted": "已成功删除所有文体-受众对", "pairsSaved": "已成功保存 {{count}} 个文体-受众对", "additionalPairsGenerated": "成功生成了 {{count}} 个额外的文体-受众对。总计：{{total}}", "validationError": "文体-受众对 {{number}}：文体和受众标题为必填项", "loadError": "无法加载文体-受众对：{{error}}", "generateError": "生成文体-受众对失败", "saveError": "保存文体-受众对失败", "noActiveModel": "请在设置中配置AI模型后再生成文体-受众对。", "contentTooShort": "文件内容过短或不适合生成文体-受众对。", "configError": "AI模型配置错误。可能缺少必要的依赖项。", "serverError": "服务器错误 ({{status}})。请稍后重试。", "emptyResponse": "生成服务返回空响应", "generationFailed": "生成失败", "saveOperationFailed": "保存操作失败", "serviceNotAvailable": "文体-受众对生成服务不可用。请检查您的API配置。", "requestFailed": "请求失败 ({{status}})。请重试。", "internalServerError": "发生内部服务器错误。", "batchGenerate": "批量生成GA对", "batchGenerateDescription": "将为选中的 {{count}} 个文件批量生成GA对，该操作可能需要一些时间。", "appendMode": "追加模式", "appendModeDescription": "为已有GA对的文件生成更多GA对，而不是覆盖", "selectAtLeastOneFile": "请先选择至少一个文件", "noDefaultModel": "未设置默认模型，请先在项目设置中配置模型", "incompleteModelConfig": "模型配置不完整，请检查模型设置", "missingApiKey": "模型未配置API密钥，请在模型设置中添加API密钥", "loadingProjectModel": "加载项目模型中...", "usingModel": "使用模型", "startGeneration": "开始生成", "batchGenCompleted": "批量生成完成！成功为 {{success}}/{{total}} 个文件生成了GA对。", "generationError": "生成过程发生错误: {{error}}", "fetchProjectInfoFailed": "获取项目信息失败: {{status}}", "fetchModelConfigFailed": "获取模型配置失败: {{status}}", "fetchProjectModelError": "获取项目模型配置时出错", "batchGenerationFailed": "批量生成GA对失败", "batchGenerationSuccess": "成功为 {{count}} 个文件生成GA对", "selectAllFiles": "全选", "deselectAllFiles": "取消全选"}}